import { AxiosInstance } from "axios";
import { getAuthToken } from "@/core/auth/lib/auth-actions";
import { KeycloakAuthAdapter } from "@/core/auth/services/keycloak-auth-adapter.service";

/**
 * Interceptor adaptativo que funciona com Keycloak e sistema tradicional
 * Adiciona automaticamente o token de autorização nas requisições
 */
export const keycloakAuthHeadersInterceptor = async (instance: AxiosInstance) => {
	instance.interceptors.request.use(async config => {
		try {
			// Verificar se Keycloak está configurado
			const keycloakAdapter = KeycloakAuthAdapter.getInstance();
			const isKeycloakAvailable = keycloakAdapter.isKeycloakAvailable();

			let token: string | undefined;

			if (isKeycloakAvailable) {
				// Usar token do Keycloak
				token = keycloakAdapter.getToken();
				
				// Se não há token no Keycloak, tentar obter do sistema tradicional
				if (!token) {
					token = await getAuthToken();
				}
			} else {
				// Usar sistema tradicional
				token = await getAuthToken();
			}

			if (token && config.headers) {
				config.headers.Authorization = `Bearer ${token}`;
			}
		} catch (error) {
			console.error("Erro ao obter token para requisição:", error);
		}

		return config;
	});
};
