import Keycloak from "keycloak-js";

// Configurações do Keycloak
export const KEYCLOAK_CONFIG = {
	url: process.env.NEXT_PUBLIC_KEYCLOAK_URL || "http://192.168.155.83:8080/",
	realm: process.env.NEXT_PUBLIC_KEYCLOAK_REALM || "master",
	clientId: process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID || "simp",
} as const;

// Opções de inicialização do Keycloak
export const KEYCLOAK_INIT_OPTIONS = {
	onLoad: "check-sso" as const,
	silentCheckSsoRedirectUri: typeof window !== "undefined" ? `${window.location.origin}/silent-check-sso.html` : undefined,
	checkLoginIframe: false,
	pkceMethod: "S256" as const,
} as const;

// Configurações de timeout para operações do Keycloak
export const KEYCLOAK_TIMEOUTS = {
	INIT: 10000,
	LOGIN: 30000,
	LOGOUT: 5000,
	TOKEN_REFRESH: 5000,
	TOKEN_CHECK: 1000,
} as const;

// Instância singleton do Keycloak
let keycloakInstance: Keycloak | null = null;

export const getKeycloakInstance = (): Keycloak => {
	if (!keycloakInstance) {
		keycloakInstance = new Keycloak(KEYCLOAK_CONFIG);
	}
	return keycloakInstance;
};
