import axios from "axios";
import { keycloakAuthHeadersInterceptor } from "./interceptors/keycloak-auth-headers.interceptor";
import { setupKeycloakRefreshInterceptor } from "./interceptors/keycloak-refresh.interceptor";

export const API_ROUTE = process.env.API_ROUTE;

export const axiosInstance = axios.create({
	baseURL: API_ROUTE,
});

keycloakAuthHeadersInterceptor(axiosInstance);
setupKeycloakRefreshInterceptor(axiosInstance);
