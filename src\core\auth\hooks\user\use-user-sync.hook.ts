"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { useEffect, useState } from "react";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { userAtom } from "../../atoms/user.atom";
import { getCurrentUser } from "../../lib/user-actions";
import { getAuthToken } from "../../lib/auth-actions";

export function useUserSync() {
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();
	const [hasToken, setHasToken] = useState<boolean | null>(null);
	useEffect(() => {
		const checkToken = async () => {
			const token = await getAuthToken();
			setHasToken(!!token);
			if (!token) {
				setIsAuthenticated(false);
				setUser(null);
			}
		};
		checkToken();
	}, [setIsAuthenticated, setUser]);

	const { data, isLoading, error } = useQuery({
		queryKey: ["user"],
		queryFn: getCurrentUser,
		enabled: hasToken === true,
		retry: false,
		staleTime: 5 * 60 * 1000,
		gcTime: 10 * 60 * 1000,
	});

	useEffect(() => {
		if (hasToken === false) {
			setIsAuthenticated(false);
			setUser(null);
		} else if (data) {
			setIsAuthenticated(!!data?.success && !!data?.data);
			setUser(data?.success && data?.data ? data.data : null);
		}
	}, [data, hasToken, setUser, setIsAuthenticated]);

	const refreshUser = async () => {
		const token = await getAuthToken();
		if (token) {
			queryClient.invalidateQueries({ queryKey: ["user"] });
		} else {
			setHasToken(false);
			setIsAuthenticated(false);
			setUser(null);
		}
	};

	return {
		user: data?.data ?? null,
		isLoading: hasToken === null || (hasToken && isLoading),
		error,
		isAuthenticated: hasToken === true && !!data?.success && !!data?.data,
		refreshUser,
	};
}
