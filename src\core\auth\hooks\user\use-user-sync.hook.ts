"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { useEffect, useState } from "react";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { userAtom } from "../../atoms/user.atom";
import { getCurrentUser } from "../../lib/user-actions";
import { getAuthToken } from "../../lib/auth-actions";
import { KeycloakAuthAdapter } from "../../services/keycloak-auth-adapter.service";

export function useUserSync() {
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();
	const [hasToken, setHasToken] = useState<boolean | null>(null);
	const [isKeycloakEnabled, setIsKeycloakEnabled] = useState(false);

	useEffect(() => {
		const checkToken = async () => {
			const keycloakAdapter = KeycloakAuthAdapter.getInstance();
			const keycloakAvailable = keycloakAdapter.isKeycloakAvailable();
			setIsKeycloakEnabled(keycloakAvailable);

			if (keycloakAvailable) {
				// Usar Keycloak para verificação
				await keycloakAdapter.initialize();
				const authenticated = keycloakAdapter.isAuthenticated();
				setHasToken(authenticated);

				if (!authenticated) {
					setIsAuthenticated(false);
					setUser(null);
				} else {
					// Se autenticado via Keycloak, obter dados do usuário do token
					const userInfo = keycloakAdapter.getUserInfo();
					if (userInfo) {
						setIsAuthenticated(true);
						setUser(userInfo);
					}
				}
			} else {
				// Usar sistema tradicional
				const token = await getAuthToken();
				setHasToken(!!token);
				if (!token) {
					setIsAuthenticated(false);
					setUser(null);
				}
			}
		};
		checkToken();
	}, [setIsAuthenticated, setUser]);

	const { data, isLoading, error } = useQuery({
		queryKey: ["user"],
		queryFn: getCurrentUser,
		enabled: hasToken === true,
		retry: false,
		staleTime: 5 * 60 * 1000,
		gcTime: 10 * 60 * 1000,
	});

	useEffect(() => {
		if (hasToken === false) {
			setIsAuthenticated(false);
			setUser(null);
		} else if (data) {
			setIsAuthenticated(!!data?.success && !!data?.data);
			setUser(data?.success && data?.data ? data.data : null);
		}
	}, [data, hasToken, setUser, setIsAuthenticated]);

	const refreshUser = async () => {
		const keycloakAdapter = KeycloakAuthAdapter.getInstance();
		const keycloakAvailable = keycloakAdapter.isKeycloakAvailable();

		if (keycloakAvailable) {
			// Usar Keycloak para refresh
			const authenticated = keycloakAdapter.isAuthenticated();
			if (authenticated) {
				const userInfo = keycloakAdapter.getUserInfo();
				if (userInfo) {
					setIsAuthenticated(true);
					setUser(userInfo);
					setHasToken(true);
				}
			} else {
				setHasToken(false);
				setIsAuthenticated(false);
				setUser(null);
			}
		} else {
			// Usar sistema tradicional
			const token = await getAuthToken();
			if (token) {
				queryClient.invalidateQueries({ queryKey: ["user"] });
			} else {
				setHasToken(false);
				setIsAuthenticated(false);
				setUser(null);
			}
		}
	};

	return {
		user: data?.data ?? null,
		isLoading: hasToken === null || (hasToken && isLoading),
		error,
		isAuthenticated: hasToken === true && !!data?.success && !!data?.data,
		refreshUser,
	};
}
