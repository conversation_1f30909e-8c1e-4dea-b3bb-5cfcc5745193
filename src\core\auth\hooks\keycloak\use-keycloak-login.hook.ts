"use client";

import { useMutation } from "@tanstack/react-query";
import { useNavigatePaths } from "@/shared/hooks/utils";
import { toast } from "@/core/toast";
import { KeycloakAuthAdapter } from "../../services/keycloak-auth-adapter.service";
import { useUserSync } from "../user/use-user-sync.hook";

export const useKeycloakLogin = () => {
	const { replaceToCurrent } = useNavigatePaths();
	const { refreshUser } = useUserSync();
	const keycloakAdapter = KeycloakAuthAdapter.getInstance();

	const mutation = useMutation({
		mutationKey: ["KeycloakLogin"],
		mutationFn: async () => {
			const result = await keycloakAdapter.login();
			if (!result.success) {
				throw new Error(result.data.message || "Falha na autenticação");
			}
			return result.data;
		},
		onSuccess: async () => {
			await refreshUser();
			replaceToCurrent();
		},
		onError: async (error) => {
			console.error("Erro no login Keycloak:", error);
			toast.error(error.message);
		},
	});

	const login = () => {
		toast.promise(
			mutation.mutateAsync(),
			{ loading: "Redirecionando para login..." },
			{ title: "Aguarde" }
		);
	};

	return { 
		login, 
		isLoading: mutation.isPending 
	};
};
