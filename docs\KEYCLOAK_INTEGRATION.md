# Integração Keycloak

Este documento descreve como configurar e usar a integração com Keycloak no sistema SIMP3.

## Visão Geral

A integração com Keycloak foi implementada de forma **adaptativa**, mantendo total compatibilidade com o sistema de autenticação existente. O sistema detecta automaticamente se o Keycloak está configurado e usa a autenticação apropriada.

## Configuração

### 1. Variáveis de Ambiente

Copie o arquivo `.env.keycloak.example` para `.env.local` e configure as variáveis:

```bash
cp .env.keycloak.example .env.local
```

### 2. Configuração Mínima

Para habilitar o Keycloak, configure estas variáveis obrigatórias:

```env
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=simp3
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=simp3-frontend
```

### 3. Configuração do Cliente Keycloak

No admin console do Keycloak:

1. Crie um novo cliente com ID `simp3-frontend`
2. Configure como **Public client**
3. Habilite **Standard Flow** (Authorization Code Flow)
4. Configure as **Valid Redirect URIs**:
   - `http://localhost:3000/*` (desenvolvimento)
   - `https://seu-dominio.com/*` (produção)
5. Configure **Web Origins**: `+` (para permitir CORS)

## Como Funciona

### Detecção Automática

O sistema verifica automaticamente se as variáveis do Keycloak estão configuradas:

- **Com Keycloak**: Usa autenticação SSO via Keycloak
- **Sem Keycloak**: Usa o sistema de login tradicional com formulário

### Componentes Adaptativos

- **AdaptiveLoginForm**: Escolhe automaticamente entre Keycloak e formulário tradicional
- **UserProfile**: Usa logout apropriado (Keycloak ou tradicional)
- **Middleware**: Valida tokens de ambos os sistemas
- **Interceptors**: Gerencia tokens e refresh automaticamente

### Hooks Disponíveis

```typescript
// Hook principal para autenticação Keycloak
const { isAuthenticated, login, logout, userInfo } = useKeycloakAuth();

// Hook para login específico
const { login, isLoading } = useKeycloakLogin();

// Hook para logout específico
const { logout, isLoggingOut } = useKeycloakLogout();

// Hook para verificação de autenticação
const { hasToken, isChecking, isKeycloakEnabled } = useKeycloakCheck();
```

## Migração

### Sem Interrupção

A integração foi projetada para **zero downtime**:

1. O sistema continua funcionando normalmente sem configuração do Keycloak
2. Quando o Keycloak é configurado, automaticamente passa a usar SSO
3. Tokens e sessões existentes continuam válidos durante a transição

### Dados do Usuário

- **Keycloak**: Obtém dados do token JWT (nome, email, roles)
- **Tradicional**: Continua usando a API `/auth/me`
- **Compatibilidade**: Ambos os formatos são mapeados para a interface `IUser`

## Desenvolvimento

### Estrutura de Arquivos

```
src/
├── config/keycloak/
│   └── config.ts                    # Configuração do Keycloak
├── core/auth/
│   ├── services/
│   │   ├── keycloak.service.ts      # Serviço principal do Keycloak
│   │   └── keycloak-auth-adapter.service.ts  # Adaptador de compatibilidade
│   ├── hooks/keycloak/              # Hooks específicos do Keycloak
│   └── components/
│       ├── keycloak-login-form.tsx  # Formulário de login SSO
│       └── adaptive-login-form.tsx  # Componente adaptativo
├── config/middleware/auth/
│   └── keycloak-auth.middleware.ts  # Middleware adaptativo
└── config/api/interceptors/
    ├── keycloak-auth-headers.interceptor.ts  # Headers adaptativos
    └── keycloak-refresh.interceptor.ts       # Refresh adaptativo
```

### Testando

1. **Sem Keycloak**: Remova as variáveis de ambiente e teste o login tradicional
2. **Com Keycloak**: Configure as variáveis e teste o SSO
3. **Transição**: Configure o Keycloak em uma sessão ativa e verifique a migração

## Troubleshooting

### Problemas Comuns

1. **Redirect Loop**: Verifique as Valid Redirect URIs no Keycloak
2. **CORS Error**: Configure Web Origins como `+` no cliente
3. **Token Inválido**: Verifique se o cliente está configurado como Public
4. **Logout não funciona**: Verifique se o Post Logout Redirect URIs está configurado

### Debug

Habilite logs do Keycloak:

```env
NEXT_PUBLIC_KEYCLOAK_ENABLE_LOGGING=true
```

### Logs

O sistema registra automaticamente:
- Inicialização do Keycloak
- Tentativas de login/logout
- Refresh de tokens
- Erros de autenticação

## Segurança

### Tokens

- **Armazenamento**: Cookies seguros com HMAC (mantém padrão existente)
- **Refresh**: Automático via interceptors
- **Expiração**: Gerenciada pelo Keycloak

### PKCE

O sistema usa PKCE (Proof Key for Code Exchange) automaticamente para maior segurança.

### Headers

Todos os headers de segurança existentes são mantidos.
