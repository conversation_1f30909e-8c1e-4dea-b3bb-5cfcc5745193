import { ApiResponse } from "@/shared/types/requests/request.type";
import { AUTH_ENDPOINTS } from "../endpoints";
import { ILoginResponse } from "./login";
import { createPostRequest } from "@/shared/lib/requests";
import { setAuthTokens } from "../../lib/auth-actions";
import { AUTH_TIMEOUTS, AUTH_RETRY_CONFIG } from "../../constants/auth-timeouts";

export async function refreshRequest({ token }: { token: string }): Promise<ApiResponse<ILoginResponse>> {
	const response = await createPostRequest<ILoginResponse>(
		AUTH_ENDPOINTS.REFRESH,
		{},
		{
			headers: {
				Authorization: `Bearer ${token}`,
			},
			timeout: AUTH_TIMEOUTS.REFRESH_REQUEST,
			retry: AUTH_RETRY_CONFIG.DEFAULT_RETRY,
			retryAttempts: AUTH_RETRY_CONFIG.REFRESH_MAX_RETRIES,
		}
	);
	if (!response.success) return response;
	await setAuthTokens(response.data.access_token);
	return response;
}
