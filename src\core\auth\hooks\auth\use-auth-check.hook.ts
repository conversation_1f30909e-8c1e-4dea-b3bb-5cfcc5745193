"use client";

import { useEffect, useState } from "react";
import { getAuthToken } from "../../lib/auth-actions";
import { KeycloakAuthAdapter } from "../../services/keycloak-auth-adapter.service";

interface IUseAuthCheckReturn {
	hasToken: boolean | null;
	isChecking: boolean;
	checkAuth: () => Promise<void>;
	isKeycloakEnabled: boolean;
}

export function useAuthCheck(): IUseAuthCheckReturn {
	const [hasToken, setHasToken] = useState<boolean | null>(null);
	const [isChecking, setIsChecking] = useState(true);
	const [isKeycloakEnabled, setIsKeycloakEnabled] = useState(false);

	const checkAuth = async () => {
		setIsChecking(true);
		try {
			const keycloakAdapter = KeycloakAuthAdapter.getInstance();
			const keycloakAvailable = keycloakAdapter.isKeycloakAvailable();
			setIsKeycloakEnabled(keycloakAvailable);

			if (keycloakAvailable) {
				// Usar Keycloak para verificação
				await keycloakAdapter.initialize();
				const authenticated = keycloakAdapter.isAuthenticated();
				setHasToken(authenticated);
			} else {
				// Usar sistema tradicional
				const token = await getAuthToken();
				setHasToken(!!token);
			}
		} catch (error) {
			console.error("Erro ao verificar token:", error);
			setHasToken(false);
		} finally {
			setIsChecking(false);
		}
	};

	useEffect(() => {
		checkAuth();
	}, []);

	return {
		hasToken,
		isChecking,
		checkAuth,
		isKeycloakEnabled,
	};
}
