import Keycloak from "keycloak-js";
import { getKeycloakInstance, KEYCLOAK_INIT_OPTIONS, KEYCLOAK_TIMEOUTS } from "@/config/keycloack/config";
import { setAuthTokens, clearAuthTokens } from "../lib/auth-actions";
import { ApiResponse } from "@/shared/types/requests/request.type";

export interface IKeycloakAuthResult {
	success: boolean;
	authenticated: boolean;
	token?: string;
	message?: string;
}

export class KeycloakService {
	private static instance: KeycloakService | null = null;
	private keycloak: Keycloak;
	private initialized = false;
	private initPromise: Promise<boolean> | null = null;

	private constructor() {
		this.keycloak = getKeycloakInstance();
	}

	public static getInstance(): KeycloakService {
		if (!KeycloakService.instance) {
			KeycloakService.instance = new KeycloakService();
		}
		return KeycloakService.instance;
	}

	public async init(): Promise<boolean> {
		if (this.initialized) {
			return this.keycloak.authenticated || false;
		}

		if (this.initPromise) {
			return this.initPromise;
		}

		this.initPromise = this.performInit();
		return this.initPromise;
	}

	private async performInit(): Promise<boolean> {
		try {
			const authenticated = await Promise.race([
				this.keycloak.init(KEYCLOAK_INIT_OPTIONS),
				new Promise<boolean>((_, reject) => 
					setTimeout(() => reject(new Error("Timeout na inicialização do Keycloak")), KEYCLOAK_TIMEOUTS.INIT)
				)
			]);

			this.initialized = true;

			if (authenticated && this.keycloak.token) {
				await this.syncTokenWithSystem(this.keycloak.token);
			}

			// Configurar refresh automático de tokens
			this.setupTokenRefresh();

			return authenticated;
		} catch (error) {
			console.error("Erro ao inicializar Keycloak:", error);
			this.initialized = false;
			return false;
		}
	}

	public async login(): Promise<IKeycloakAuthResult> {
		try {
			if (!this.initialized) {
				await this.init();
			}

			await Promise.race([
				this.keycloak.login(),
				new Promise<never>((_, reject) => 
					setTimeout(() => reject(new Error("Timeout no login")), KEYCLOAK_TIMEOUTS.LOGIN)
				)
			]);

			if (this.keycloak.authenticated && this.keycloak.token) {
				await this.syncTokenWithSystem(this.keycloak.token);
				return {
					success: true,
					authenticated: true,
					token: this.keycloak.token,
					message: "Login realizado com sucesso"
				};
			}

			return {
				success: false,
				authenticated: false,
				message: "Falha na autenticação"
			};
		} catch (error) {
			console.error("Erro no login:", error);
			return {
				success: false,
				authenticated: false,
				message: error instanceof Error ? error.message : "Erro desconhecido no login"
			};
		}
	}

	public async logout(): Promise<ApiResponse<{ message: string }>> {
		try {
			await Promise.race([
				this.keycloak.logout(),
				new Promise<never>((_, reject) => 
					setTimeout(() => reject(new Error("Timeout no logout")), KEYCLOAK_TIMEOUTS.LOGOUT)
				)
			]);

			await clearAuthTokens();

			return {
				success: true,
				data: { message: "Logout realizado com sucesso" },
				status: 200
			};
		} catch (error) {
			console.error("Erro no logout:", error);
			await clearAuthTokens(); // Limpar tokens mesmo em caso de erro
			return {
				success: false,
				data: { message: error instanceof Error ? error.message : "Erro no logout" },
				status: 500
			};
		}
	}

	public async refreshToken(): Promise<boolean> {
		try {
			if (!this.initialized || !this.keycloak.authenticated) {
				return false;
			}

			const refreshed = await Promise.race([
				this.keycloak.updateToken(30), // Refresh se expira em menos de 30 segundos
				new Promise<boolean>((_, reject) => 
					setTimeout(() => reject(new Error("Timeout no refresh")), KEYCLOAK_TIMEOUTS.TOKEN_REFRESH)
				)
			]);

			if (refreshed && this.keycloak.token) {
				await this.syncTokenWithSystem(this.keycloak.token);
				return true;
			}

			return false;
		} catch (error) {
			console.error("Erro ao atualizar token:", error);
			return false;
		}
	}

	public isAuthenticated(): boolean {
		return this.initialized && (this.keycloak.authenticated || false);
	}

	public getToken(): string | undefined {
		return this.keycloak.token;
	}

	public getUserInfo() {
		if (!this.isAuthenticated()) {
			return null;
		}

		return {
			id: this.keycloak.tokenParsed?.sub || "",
			name: this.keycloak.tokenParsed?.name || this.keycloak.tokenParsed?.preferred_username || "",
			email: this.keycloak.tokenParsed?.email || "",
			roles: this.keycloak.tokenParsed?.realm_access?.roles || []
		};
	}

	private async syncTokenWithSystem(token: string): Promise<void> {
		try {
			await setAuthTokens(token);
		} catch (error) {
			console.error("Erro ao sincronizar token com o sistema:", error);
		}
	}

	private setupTokenRefresh(): void {
		// Configurar refresh automático quando o token está próximo do vencimento
		this.keycloak.onTokenExpired = () => {
			console.log("Token expirado, tentando renovar...");
			this.refreshToken().catch(error => {
				console.error("Falha ao renovar token automaticamente:", error);
			});
		};
	}
}
