import { AxiosInstance, AxiosResponse, AxiosRequestConfig as OriginalAxiosRequestConfig } from "axios";
import { KeycloakAuthAdapter } from "@/core/auth/services/keycloak-auth-adapter.service";
import RefreshService from "@/core/auth/services/refresh-service";

interface AxiosRequestConfig extends OriginalAxiosRequestConfig {
	_retry?: boolean;
}

/**
 * Interceptor de refresh adaptativo que funciona com Keycloak e sistema tradicional
 */
export const setupKeycloakRefreshInterceptor = (instance: AxiosInstance): void => {
	const keycloakAdapter = KeycloakAuthAdapter.getInstance();
	const refreshService = new RefreshService(instance);
	const isKeycloakAvailable = keycloakAdapter.isKeycloakAvailable();

	instance.interceptors.response.use(
		response => response,
		async error => {
			const originalRequest = error.config;

			if (
				error.response?.status === 401 &&
				!originalRequest._retry &&
				originalRequest.url &&
				!refreshService.isNonRefreshableEndpoint(originalRequest.url)
			) {
				try {
					if (isKeycloakAvailable) {
						// Usar refresh do Keycloak
						return await handleKeycloakRefresh(originalRequest, keycloakAdapter, instance);
					} else {
						// Usar refresh tradicional
						return await refreshService.handleTokenRefresh(originalRequest);
					}
				} catch (refreshError) {
					if (refreshError instanceof Error) {
						return Promise.reject(refreshError);
					}
					return Promise.reject(new Error(String(refreshError)));
				}
			}

			return Promise.reject(error instanceof Error ? error : new Error(String(error)));
		}
	);
};

async function handleKeycloakRefresh(
	originalRequest: AxiosRequestConfig,
	keycloakAdapter: KeycloakAuthAdapter,
	axiosInstance: AxiosInstance
): Promise<AxiosResponse> {
	originalRequest._retry = true;

	try {
		// Tentar refresh do token via Keycloak
		const refreshResult = await keycloakAdapter.refreshToken();

		if (refreshResult.success) {
			const newToken = keycloakAdapter.getToken();
			if (newToken) {
				originalRequest.headers = {
					...originalRequest.headers,
					Authorization: `Bearer ${newToken}`,
				};
				return axiosInstance(originalRequest);
			}
		}

		// Se o refresh falhou, limpar tokens e rejeitar
		await keycloakAdapter.logout();
		throw new Error("Token expirado. Por favor, faça login novamente.");
	} catch (error) {
		await keycloakAdapter.logout();
		throw error instanceof Error ? error : new Error(String(error));
	}
}
