"use client";

import { useEffect, useState } from "react";
import { LoginForm } from "./login-form";
import { KeycloakLoginForm } from "./keycloak-login-form";
import { KeycloakAuthAdapter } from "../services/keycloak-auth-adapter.service";

export const AdaptiveLoginForm = () => {
	const [isKeycloakAvailable, setIsKeycloakAvailable] = useState<boolean | null>(null);

	useEffect(() => {
		const adapter = KeycloakAuthAdapter.getInstance();
		setIsKeycloakAvailable(adapter.isKeycloakAvailable());
	}, []);

	// Mostrar loading enquanto verifica a disponibilidade do Keycloak
	if (isKeycloakAvailable === null) {
		return (
			<div className="flex flex-col items-center gap-6 w-full px-4">
				<div className="text-center">
					<p>Carregando...</p>
				</div>
			</div>
		);
	}

	// Se Keycloak está disponível, usar o componente Keycloak
	if (isKeycloakAvailable) {
		return <KeycloakLoginForm />;
	}

	// Caso contrário, usar o formulário tradicional
	return <LoginForm />;
};
