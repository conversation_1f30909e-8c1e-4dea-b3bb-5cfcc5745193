"use client";

import { But<PERSON> } from "@/shared/components/shadcn/button";
import { useKeycloakLogin } from "../hooks/keycloak/use-keycloak-login.hook";
import { KeycloakAuthAdapter } from "../services/keycloak-auth-adapter.service";
import { useEffect, useState } from "react";
import { LogIn } from "lucide-react";

export const KeycloakLoginForm = () => {
	const { login, isLoading } = useKeycloakLogin();
	const [isKeycloakAvailable, setIsKeycloakAvailable] = useState(false);

	useEffect(() => {
		const adapter = KeycloakAuthAdapter.getInstance();
		setIsKeycloakAvailable(adapter.isKeycloakAvailable());
	}, []);

	if (!isKeycloakAvailable) {
		return null; // Não renderizar se Keycloak não estiver disponível
	}

	return (
		<div className="flex flex-col items-center gap-6 w-full px-4">
			<div className="text-center space-y-2">
				<h2 className="text-2xl font-semibold">Bem-vindo</h2>
				<p className="text-gray-600">Faça login para continuar</p>
			</div>
			
			<Button
				onClick={login}
				disabled={isLoading}
				className="w-full max-w-[21.62rem] h-[2.87rem] flex items-center gap-2"
			>
				<LogIn size={18} />
				{isLoading ? "Redirecionando..." : "Entrar com SSO"}
			</Button>
			
			<p className="text-sm text-gray-500 text-center max-w-[21.62rem]">
				Você será redirecionado para a página de login segura
			</p>
		</div>
	);
};
