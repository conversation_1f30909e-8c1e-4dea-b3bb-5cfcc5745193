import { NextRequest, NextResponse } from "next/server";
import { AccessTokenValidator } from "@/config/services/access-token-validator";

/**
 * Middleware de autenticação adaptativo que funciona com Keycloak e sistema tradicional
 * Verifica se o Keycloak está configurado e usa a validação apropriada
 */
export async function keycloakAuthMiddleware(request: Readonly<NextRequest>): Promise<NextResponse> {
	const { pathname } = request.nextUrl;
	
	// Verificar se Keycloak está configurado
	const isKeycloakConfigured = !!(
		process.env.NEXT_PUBLIC_KEYCLOAK_URL &&
		process.env.NEXT_PUBLIC_KEYCLOAK_REALM &&
		process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID
	);

	if (isKeycloakConfigured) {
		// Com Keycloak configurado, usar validação de token padrão
		// O token será validado pelo sistema existente que já funciona com JWTs
		const accessTokenValidator = new AccessTokenValidator();
		const validationResult = await accessTokenValidator.validate(request);

		if (!validationResult.isValid || validationResult.status !== "ok") {
			const loginUrl = new URL("/login", request.url);
			loginUrl.searchParams.set("redirect", pathname);
			return NextResponse.rewrite(loginUrl);
		}

		return NextResponse.next();
	} else {
		// Sem Keycloak, usar o middleware de autenticação tradicional
		const { authMiddleware } = await import("./auth.middleware");
		return authMiddleware(request);
	}
}
