"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { userAtom } from "../../atoms/user.atom";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { toast } from "@/core/toast";
import { KeycloakAuthAdapter } from "../../services/keycloak-auth-adapter.service";

interface IUseKeycloakLogoutReturn {
	logout: () => void;
	isLoggingOut: boolean;
}

export function useKeycloakLogout(): IUseKeycloakLogoutReturn {
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();
	const keycloakAdapter = KeycloakAuthAdapter.getInstance();

	const mutation = useMutation({
		mutationKey: ["keycloakLogout"],
		mutationFn: async () => {
			const result = await keycloakAdapter.logout();
			
			// Limpar estado local independentemente do resultado
			setUser(null);
			setIsAuthenticated(false);
			queryClient.clear();
			
			if (!result.success) {
				throw new Error(result.data.message || "Erro no logout");
			}
			
			return true;
		},
		onSuccess: () => {
			toast.success("Logout realizado com sucesso");
		},
		onError: (error) => {
			console.error("Erro no logout:", error);
			toast.error("Erro ao fazer logout");
		},
	});

	return {
		logout: () => mutation.mutate(),
		isLoggingOut: mutation.isPending,
	};
}
