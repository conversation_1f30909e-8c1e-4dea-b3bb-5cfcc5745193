"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { clearAuthTokens } from "../../lib/auth-actions";
import { userAtom } from "../../atoms/user.atom";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { toast } from "@/core/toast";

interface IUseLogoutReturn {
	logout: () => void;
	isLoggingOut: boolean;
}

export function useLogout(): IUseLogoutReturn {
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationKey: ["logout"],
		mutationFn: async () => {
			await clearAuthTokens();
			setUser(null);
			setIsAuthenticated(false);
			queryClient.clear();
			return true;
		},
		onSuccess: () => {
			toast.success("Logout realizado com sucesso");
		},
		onError: error => {
			console.error("Erro no logout:", error);
			toast.error("Erro ao fazer logout");
		},
	});

	return {
		logout: () => mutation.mutate(),
		isLoggingOut: mutation.isPending,
	};
}
