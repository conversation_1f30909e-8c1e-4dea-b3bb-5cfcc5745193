import { KeycloakService } from "./keycloak.service";
import { AuthCookieManager } from "./auth-cookie.service";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { ILoginResponse } from "../api/requests/login";
import { IRole, ROLES } from "@/config/enums/role.enum";

/**
 * Adaptador que mantém compatibilidade entre o sistema Keycloak e o sistema atual
 * Permite que o sistema continue funcionando com as mesmas interfaces
 */
export class KeycloakAuthAdapter {
	private static instance: KeycloakAuthAdapter | null = null;
	private keycloakService: KeycloakService;

	private constructor() {
		this.keycloakService = KeycloakService.getInstance();
	}

	public static getInstance(): KeycloakAuthAdapter {
		if (!KeycloakAuthAdapter.instance) {
			KeycloakAuthAdapter.instance = new KeycloakAuthAdapter();
		}
		return KeycloakAuthAdapter.instance;
	}

	/**
	 * Inicializa o Keycloak e verifica se há uma sessão ativa
	 */
	public async initialize(): Promise<boolean> {
		try {
			const authenticated = await this.keycloakService.init();

			if (authenticated) {
				// Se já está autenticado, sincronizar com o sistema atual
				const token = this.keycloakService.getToken();
				if (token) {
					await AuthCookieManager.setAuthTokens(token);
				}
			}

			return authenticated;
		} catch (error) {
			console.error("Erro ao inicializar adaptador Keycloak:", error);
			return false;
		}
	}

	/**
	 * Login via Keycloak - mantém compatibilidade com a interface atual
	 */
	public async login(): Promise<ApiResponse<ILoginResponse>> {
		try {
			const result = await this.keycloakService.login();

			if (result.success && result.token) {
				return {
					success: true,
					data: { access_token: result.token },
					status: 200,
				};
			}

			return {
				success: false,
				data: { message: result.message || "Falha na autenticação" },
				status: 401,
			};
		} catch (error) {
			return {
				success: false,
				data: { message: error instanceof Error ? error.message : "Erro no login" },
				status: 500,
			};
		}
	}

	/**
	 * Logout via Keycloak
	 */
	public async logout(): Promise<ApiResponse<{ message: string }>> {
		return this.keycloakService.logout();
	}

	/**
	 * Refresh de token via Keycloak
	 */
	public async refreshToken(): Promise<ApiResponse<ILoginResponse>> {
		try {
			const refreshed = await this.keycloakService.refreshToken();

			if (refreshed) {
				const token = this.keycloakService.getToken();
				if (token) {
					return {
						success: true,
						data: { access_token: token },
						status: 200,
					};
				}
			}

			return {
				success: false,
				data: { message: "Falha ao renovar token" },
				status: 401,
			};
		} catch (error) {
			return {
				success: false,
				data: { message: error instanceof Error ? error.message : "Erro ao renovar token" },
				status: 500,
			};
		}
	}

	/**
	 * Verifica se o usuário está autenticado
	 */
	public isAuthenticated(): boolean {
		return this.keycloakService.isAuthenticated();
	}

	/**
	 * Obtém informações do usuário do token Keycloak
	 * Mantém compatibilidade com a estrutura atual
	 */
	public getUserInfo() {
		const userInfo = this.keycloakService.getUserInfo();

		if (!userInfo) {
			return null;
		}

		// Mapear roles do Keycloak para IRole
		// Por enquanto, usar um valor padrão ou mapear conforme necessário
		const permissions: IRole[] = userInfo.roles.map(() => ROLES.GERENCIA); // GERENCIA como padrão

		return {
			id: userInfo.id,
			name: userInfo.name,
			permissions,
		};
	}

	/**
	 * Obtém o token atual
	 */
	public getToken(): string | undefined {
		return this.keycloakService.getToken();
	}

	/**
	 * Verifica se o Keycloak está disponível e configurado
	 */
	public isKeycloakAvailable(): boolean {
		try {
			// Verificar se as variáveis de ambiente estão configuradas
			const hasConfig = !!(
				process.env.NEXT_PUBLIC_KEYCLOAK_URL &&
				process.env.NEXT_PUBLIC_KEYCLOAK_REALM &&
				process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID
			);

			return hasConfig;
		} catch {
			return false;
		}
	}
}
