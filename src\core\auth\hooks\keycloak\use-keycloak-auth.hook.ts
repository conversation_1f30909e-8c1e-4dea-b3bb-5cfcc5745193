"use client";

import { useEffect, useState, useCallback } from "react";
import { KeycloakAuthAdapter } from "../../services/keycloak-auth-adapter.service";
import { useSetAtom } from "jotai";
import { userAtom } from "../../atoms/user.atom";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { useQueryClient } from "@tanstack/react-query";

interface IUseKeycloakAuthReturn {
	isInitialized: boolean;
	isAuthenticated: boolean;
	isLoading: boolean;
	login: () => Promise<void>;
	logout: () => Promise<void>;
	checkAuth: () => Promise<void>;
	userInfo: any;
	error: string | null;
}

export function useKeycloakAuth(): IUseKeycloakAuthReturn {
	const [isInitialized, setIsInitialized] = useState(false);
	const [isAuthenticated, setIsAuthenticated] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [userInfo, setUserInfo] = useState(null);
	const [error, setError] = useState<string | null>(null);

	const setUser = useSetAtom(userAtom);
	const setIsAuthenticatedAtom = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();

	const keycloakAdapter = KeycloakAuthAdapter.getInstance();

	const updateAuthState = useCallback((authenticated: boolean, user: any = null) => {
		setIsAuthenticated(authenticated);
		setIsAuthenticatedAtom(authenticated);
		setUserInfo(user);
		setUser(user);
	}, [setIsAuthenticatedAtom, setUser]);

	const checkAuth = useCallback(async () => {
		try {
			setIsLoading(true);
			setError(null);

			if (!isInitialized) {
				const initialized = await keycloakAdapter.initialize();
				setIsInitialized(true);

				if (initialized) {
					const user = keycloakAdapter.getUserInfo();
					updateAuthState(true, user);
				} else {
					updateAuthState(false);
				}
			} else {
				const authenticated = keycloakAdapter.isAuthenticated();
				if (authenticated) {
					const user = keycloakAdapter.getUserInfo();
					updateAuthState(true, user);
				} else {
					updateAuthState(false);
				}
			}
		} catch (err) {
			console.error("Erro ao verificar autenticação:", err);
			setError(err instanceof Error ? err.message : "Erro desconhecido");
			updateAuthState(false);
		} finally {
			setIsLoading(false);
		}
	}, [isInitialized, keycloakAdapter, updateAuthState]);

	const login = useCallback(async () => {
		try {
			setIsLoading(true);
			setError(null);

			const result = await keycloakAdapter.login();
			
			if (result.success) {
				const user = keycloakAdapter.getUserInfo();
				updateAuthState(true, user);
				
				// Invalidar queries para recarregar dados do usuário
				queryClient.invalidateQueries({ queryKey: ["user"] });
			} else {
				setError("Falha no login");
				updateAuthState(false);
			}
		} catch (err) {
			console.error("Erro no login:", err);
			setError(err instanceof Error ? err.message : "Erro no login");
			updateAuthState(false);
		} finally {
			setIsLoading(false);
		}
	}, [keycloakAdapter, updateAuthState, queryClient]);

	const logout = useCallback(async () => {
		try {
			setIsLoading(true);
			setError(null);

			await keycloakAdapter.logout();
			updateAuthState(false);
			
			// Limpar cache de queries
			queryClient.clear();
		} catch (err) {
			console.error("Erro no logout:", err);
			setError(err instanceof Error ? err.message : "Erro no logout");
			// Mesmo com erro, limpar estado local
			updateAuthState(false);
			queryClient.clear();
		} finally {
			setIsLoading(false);
		}
	}, [keycloakAdapter, updateAuthState, queryClient]);

	// Inicializar automaticamente quando o hook é montado
	useEffect(() => {
		if (!keycloakAdapter.isKeycloakAvailable()) {
			setError("Keycloak não está configurado");
			setIsLoading(false);
			return;
		}

		checkAuth();
	}, [checkAuth, keycloakAdapter]);

	return {
		isInitialized,
		isAuthenticated,
		isLoading,
		login,
		logout,
		checkAuth,
		userInfo,
		error
	};
}
